<template>
  <view class="poster-display-container">
    

    <!-- 海报图片 -->
    <view class="poster-content">
      <image 
        class="poster-image" 
        :src="processImage(posterUrl)" 
        mode="widthFix"
        
      ></image>
      
      <!-- 提示文字 -->
      <view class="poster-tip">长按图片保存到相册</view>
    </view>


   
  </view>
</template>

<script>
	import {  processImage } from '@/utils/utils.js'
export default {
  data() {
    return {
      posterUrl: '',
      loading: true
    }
  },

  onLoad(options) {
    if (options.posterUrl) {
      this.posterUrl = decodeURIComponent(options.posterUrl)
    } else {
      uni.showToast({
        title: '海报地址错误',
        icon: 'none'
      })
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
    }
  },

  

  methods: {
    // 返回上一页
    goBack() {
      uni.navigateBack()
    },

    // 图片加载完成
    onImageLoad() {
      this.loading = false
    },

    // 图片加载失败
    onImageError() {
      this.loading = false
      uni.showToast({
        title: '图片加载失败',
        icon: 'none'
      })
    },

    processImage
  }
}
</script>

<style scoped>
.poster-display-container {
  background-color: #000;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 导航栏 */
.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 30rpx;
  background-color: #000;
  color: #fff;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
}

.nav-back {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-back-icon {
  font-size: 36rpx;
  color: #fff;
}

.nav-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #fff;
}

.nav-right {
  width: 60rpx;
}

/* 海报内容 */
.poster-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx 160rpx;
  min-height: calc(100vh - 280rpx);
}

.poster-image {
  width: 100%;
  max-width: 600rpx;
  border-radius: 12rpx;
  box-shadow: 0 8rpx 32rpx rgba(255, 255, 255, 0.1);
}

.poster-tip {
  color: rgba(255, 255, 255, 0.7);
  font-size: 28rpx;
  margin-top: 40rpx;
  text-align: center;
}

/* 底部操作按钮 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #000;
  padding: 30rpx 40rpx;
  padding-bottom: calc(30rpx + env(safe-area-inset-bottom));
  display: flex;
  gap: 20rpx;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
}

.save-btn {
  background-color: #007AFF;
  color: #fff;
}

.save-btn:active {
  background-color: #0056CC;
}

.share-btn {
  background-color: rgba(255, 255, 255, 0.1);
  color: #fff;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.share-btn:active {
  background-color: rgba(255, 255, 255, 0.2);
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #000;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 200;
}

.loading-content {
  text-align: center;
}

.loading-text {
  color: #fff;
  font-size: 28rpx;
}
</style>
