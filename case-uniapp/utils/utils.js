// 工具函数
import { getAvatarUrl, getCaseImageUrl } from "./imageConfig.js";

// 格式化时间
export const formatTime = (timestamp) => {
  const date = new Date(timestamp);
  const now = new Date();
  const diff = now - date;

  // 小于1分钟
  if (diff < 60000) {
    return "刚刚";
  }

  // 小于1小时
  if (diff < 3600000) {
    return Math.floor(diff / 60000) + "分钟前";
  }

  // 小于1天
  if (diff < 86400000) {
    return Math.floor(diff / 3600000) + "小时前";
  }

  // 小于7天
  if (diff < 604800000) {
    return Math.floor(diff / 86400000) + "天前";
  }

  // 格式化为日期
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");

  if (year === now.getFullYear()) {
    return `${month}-${day}`;
  }

  return `${year}-${month}-${day}`;
};

// 处理图片数组
export const processImages = (imageStr, size = "medium") => {
  if (!imageStr) return [];
  return imageStr
    .split(",")
    .filter((img) => img.trim())
    .map((img) => getCaseImageUrl(img.trim(), size));
};

// 处理图片数组
export const processImage = (imageStr, size = "medium") => {
  if (!imageStr) return "";
  return getCaseImageUrl(imageStr, size);
};

// 处理头像URL
export const processAvatarUrl = (avatarUrl, size = "medium") => {
  return getAvatarUrl(avatarUrl, size);
};

// 处理标签数组
export const processTags = (tagStr) => {
  if (!tagStr) return [];
  return tagStr.split(",").filter((tag) => tag.trim());
};

// 截取文本
export const truncateText = (text, maxLength = 100) => {
  if (!text) return "";
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + "...";
};

// 去除HTML标签
export const stripHtml = (html) => {
  if (!html) return "";
  return html.replace(/<[^>]*>/g, "");
};

// 处理案例内容（去除HTML标签并截取）
export const processCaseContent = (content, maxLength = 150) => {
  const plainText = stripHtml(content);
  return truncateText(plainText, maxLength);
};

// 图片预览
export const previewImages = (images, current = 0) => {
  if (!images || images.length === 0) return;

  // 确保所有图片URL都是完整的
  const processedImages = images.map((img) => {
    // 如果传入的已经是处理过的URL数组，直接使用
    if (typeof img === "string") {
      // 检查是否已经是完整的URL
      if (
        img.startsWith("http://") ||
        img.startsWith("https://") ||
        img.startsWith("/static/")
      ) {
        return img;
      }
      // 如果不是完整URL，则进行处理
      return getCaseImageUrl(img, "large"); // 预览时使用大图
    }
    return img;
  });

  uni.previewImage({
    urls: processedImages,
    current: current,
    fail: (err) => {
      console.error("图片预览失败:", err);
      uni.showToast({
        title: "图片预览失败",
        icon: "none",
      });
    },
  });
};
